import React, { useEffect, useState, useRef } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card.tsx";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table.tsx";
import { Badge } from "@/components/ui/badge.tsx";
import { useAuth } from "@/features/auth/hooks/useAuth.ts";
import { apiService } from "@/services/api.ts";
import { useUIHelpers } from "@/hooks/use-ui-helpers";
import { Loader2, CheckCircle, XCircle } from "lucide-react";
import { Pagination } from "@/components/ui/pagination.tsx";
import { useNavigate } from 'react-router-dom';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  Al<PERSON>Dial<PERSON>Footer,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON><PERSON><PERSON>,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";

interface CertificateItemType {
  id: number | string;
  name: string;
  email: string;
  slug: string;
  system_reserve: number;
  served: number;
  phone: null;
  code: null;
  provider_id: null;
  status: number;
  is_featured: number;
  is_verified: number;
  type: null;
  email_verified_at: null;
  fcm_token: null;
  experience_interval: null;
  experience_duration: null;
  description: null;
  created_by: number;
  created_at: string;
  updated_at: string;
  deleted_at: null;
  company_id: null;
  location_cordinates: null;
  certificates: {
    uuid: string;
    file_name: string;
    url: string;
    mime_type: string;
    file_size: number;
    collection_name: string;
    custom_properties: { uploaded_by: null; original_name: string }
  }[];
  certificates_status: string;
  bookings_count: number;
  reviews_count: number;
  role: {
    id: number;
    name: string;
    guard_name: string;
    system_reserve: number;
    created_at: string;
    updated_at: string;
    pivot: { model_type: string; model_id: number; role_id: number }
  };
  review_ratings: number;
  provider_rating_list: number[];
  service_man_rating_list: number[];
  primary_address: null;
  total_days_experience: number;
  ServicemanReviewRatings: number;
  media: never[];
  wallet: null;
  provider_wallet: null;
  serviceman_wallet: null;
  known_languages: never[];
  expertise: never[];
  zones: never[];
  provider: null;
  roles: {
    id: number;
    name: string;
    guard_name: string;
    system_reserve: number;
    created_at: string;
    updated_at: string;
    pivot: { model_type: string; model_id: number; role_id: number }
  }[];
  reviews: never[];
  servicemanreviews: never[];
}

// Define the response type for the certificates API
interface CertificateResponseType {
  success: boolean;
  data: CertificateItemType[];
  pagination: {
    current_page: number
    last_page: number
    per_page: number
    total: number
  }
}

export const Certificates = () => {
  const { token } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [certificates, setCertificates] = useState<CertificateItemType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const { isMobile } = useUIHelpers();
  const certificatesListRef = useRef<HTMLDivElement>(null);
  const [pagination, setPagination] = useState({
    current_page: 1,
    last_page: 1,
    per_page: 10,
    total: 0
  });

  // State for confirmation dialog
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [confirmAction, setConfirmAction] = useState<'approve' | 'reject' | null>(null);
  const [selectedCertificateId, setSelectedCertificateId] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    fetchCertificates(currentPage);
  }, [token, currentPage]);

  const fetchCertificates = async (page = 1) => {
    setIsLoading(true);
    try {
      const endpoint = `/api/admin/certificates/reviews?page=${page}`;
      const response = await apiService<CertificateResponseType>(endpoint, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': token || ''
        },
      });

      if (!response.data?.success) {
        throw new Error(`Response status: ${response.status}`);
      }

      const json = response.data;
      if (!json) {
        throw new Error('No data received');
      }

      setCertificates(json.data);

      // Store pagination data
      if (json.pagination) {
        setPagination(json.pagination);
      }
    } catch (error) {
      console.error((error as Error).message || 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  // Function to open confirmation dialog
  const openConfirmDialog = (action: 'approve' | 'reject', id: string | number) => {
    setConfirmAction(action);
    setSelectedCertificateId(id.toString());
    setConfirmDialogOpen(true);
  };

  // Function to handle the confirmation action
  const handleConfirmAction = async () => {
    if (!selectedCertificateId || !confirmAction) return;

    setActionLoading(true);
    try {
      const endpoint = `/api/admin/certificates/reviews/${selectedCertificateId}/${confirmAction}`;
      const response = await apiService(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': token || ''
        },
      });

      if (response.isSuccess) {
        const certIndex = certificates.findIndex(cert => cert.id.toString() === selectedCertificateId);
        if (certIndex !== -1) {
          const updatedCertificates = [...certificates];
          updatedCertificates[certIndex] = {
            ...updatedCertificates[certIndex],
            certificates_status: confirmAction === 'approve' ? 'approved' : 'rejected'
          };
          setCertificates(updatedCertificates);
        }

        if (confirmAction === 'approve') {
          // Find the approved certificate to get provider information
          const approvedCertificate = certificates.find(cert => cert.id.toString() === selectedCertificateId);
          if (approvedCertificate) {
            // Show success message
            toast({
              title: "Certificate Approved!",
              description: "Redirecting to link businesses..."
            });

            // Redirect to ProvidersTable with certificate and provider information
            navigate(`/admin/providers?certificateApproval=true&certificateId=${selectedCertificateId}&providerId=${approvedCertificate.id}&providerName=${encodeURIComponent(approvedCertificate.name)}&providerEmail=${encodeURIComponent(approvedCertificate.email)}`);
            return; // Exit early to avoid showing the regular success toast
          }
        }

        toast({
          title: "Successfully!",
          description: `Certificate ${confirmAction === 'approve' ? 'approved' : 'rejected'} successfully`
        });
      } else {
        toast({
          title: "Error",
          description: response.error,
          variant: "destructive",
        });
      }
      setConfirmDialogOpen(false);
    } catch (error) {
      // Show error notification
      toast({
        title: "Error",
        description: `Failed to ${confirmAction} certificate. Please try again.`,
        variant: "destructive",
      });
      setConfirmDialogOpen(false);
    } finally {
      setActionLoading(false);
      setSelectedCertificateId(null);
      setConfirmAction(null);
    }
  };

  // Function to trigger approve confirmation
  const handleApprove = (id: string | number) => {
    openConfirmDialog('approve', id);
  };

  // Function to trigger reject confirmation
  const handleReject = (id: string | number) => {
    openConfirmDialog('reject', id);
  };

  // Handler for page changes
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    if (certificatesListRef.current) {
      certificatesListRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Function to get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return <Badge variant="success">Approved</Badge>;
      case "rejected":
        return <Badge variant="destructive">Rejected</Badge>;
      case "pending":
        return <Badge variant="warning">Pending</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // Render certificate card for mobile view
  const CertificateCard = ({ certificate }: { certificate: CertificateItemType }) => {
    return (
      <div className="mb-4 rounded-lg shadow-sm overflow-hidden border border-gray-200">
        <div className="p-4">
          <div className="flex justify-between items-start mb-3">
            <div className="flex-1">
              <h3 className="font-medium text-gray-900 truncate flex items-center">
                {certificate.certificates_status === "approved" && (
                  <CheckCircle className="h-4 w-4 text-green-600 mr-1.5 flex-shrink-0" />
                )}
                {certificate.certificates_status === "rejected" && (
                  <XCircle className="h-4 w-4 text-red-600 mr-1.5 flex-shrink-0" />
                )}
                {certificate.name}
              </h3>
              <p className="text-xs text-gray-600 mt-0.5 truncate">{certificate.role?.name}</p>
            </div>
            <div>
              {getStatusBadge(certificate.certificates_status)}
            </div>
          </div>

          <div className="space-y-2 mb-3">
            <p className="text-sm text-gray-600">
              <span className="font-medium">Email:</span> {certificate.email}
            </p>
            <p className="text-sm text-gray-600">
              <span className="font-medium">Phone:</span> {certificate.phone || "N/A"}
            </p>
            <p className="text-sm text-gray-600">
              <span className="font-medium">Certificate:</span>{' '}
              {certificate.certificates.length > 0 ? (
                <a
                  href={certificate.certificates[0].url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                >
                  {certificate.certificates[0].file_name}
                </a>
              ) : (
                "No certificate"
              )}
            </p>
          </div>

          {certificate.certificates_status === 'pending' && (
            <div className="flex gap-2 mt-3">
              <Button
                variant="success"
                size="sm"
                className="flex-1"
                onClick={() => handleApprove(certificate.id)}
                disabled={actionLoading}
              >
                Approve
              </Button>
              <Button
                variant="destructive"
                size="sm"
                className="flex-1"
                onClick={() => handleReject(certificate.id)}
                disabled={actionLoading}
              >
                Reject
              </Button>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <Card className="shadow-md">
      <CardHeader className="pb-3">
        <CardTitle className="text-xl md:text-2xl flex justify-between items-center">
          <span>Business Certificates</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div ref={certificatesListRef}></div>
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
        ) : isMobile ? (
          // Mobile view with cards
          <div className="space-y-1">
            {certificates.length === 0 ? (
              <div className="text-center py-10 bg-gray-50 rounded-lg">
                <p className="text-gray-500">No certificates found</p>
              </div>
            ) : (
              certificates.map((certificate) => (
                <CertificateCard key={certificate.id.toString()} certificate={certificate} />
              ))
            )}

            {certificates.length > 0 && (
              <div className="mt-4">
                <Pagination
                  totalItems={pagination.total}
                  itemsPerPage={pagination.per_page}
                  currentPage={pagination.current_page}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </div>
        ) : (
          // Desktop view with table
          <>
            <Table>
              <TableHeader className="bg-gray-100">
                <TableRow>
                  <TableHead>Business Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Certificate</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {certificates.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      No certificates found
                    </TableCell>
                  </TableRow>
                ) : (
                  certificates.map((certificate) => (
                    <TableRow 
                      key={certificate.id.toString()}
                      className={
                        certificate.certificates_status === "approved" 
                          ? "bg-green-50" 
                          : certificate.certificates_status === "rejected" 
                            ? "bg-red-50" 
                            : ""
                      }
                    >
                      <TableCell>
                        <div className="font-medium flex items-center">
                          {certificate.certificates_status === "approved" && (
                            <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                          )}
                          {certificate.certificates_status === "rejected" && (
                            <XCircle className="h-5 w-5 text-red-600 mr-2" />
                          )}
                          {certificate.name}
                        </div>
                      </TableCell>
                      <TableCell>{certificate.email}</TableCell>
                      <TableCell>{certificate.role?.name}</TableCell>
                      <TableCell>
                        <div className="flex flex-col gap-1" style={{ maxWidth: '200px' }}>
                          {
                            certificate.certificates.map((item, index) => (
                              <a
                                key={item.uuid || index}
                                href={item.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:underline"
                              >
                                {item.file_name}
                              </a>
                            ))
                          }
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="success"
                            className="border-green-700 text-green-700 bg-white border border-solid hover:bg-green-700 hover:text-white"
                            size="sm"
                            disabled={certificate.certificates_status === 'approved'}
                            onClick={() => handleApprove(certificate.id)}
                          >
                            Approve
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            disabled={certificate.certificates_status === "rejected"}
                            onClick={() => handleReject(certificate.id)}
                          >
                            Reject
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>

            {certificates.length > 0 && (
              <div className="mt-4">
                <Pagination
                  totalItems={pagination.total}
                  itemsPerPage={pagination.per_page}
                  currentPage={pagination.current_page}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </>
        )}
      </CardContent>

      {/* Confirmation Dialog */}
      <AlertDialog open={confirmDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Action</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to {confirmAction} this certificate?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              disabled={actionLoading}
              onClick={()=>setConfirmDialogOpen(false)}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmAction}
              disabled={actionLoading}
              className={confirmAction === 'approve' ? 'bg-green-600 hover:!bg-green-700' : 'bg-red-600 hover:!bg-red-700'}
            >
              {actionLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                confirmAction === 'approve' ? 'Approve' : 'Reject'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
};
