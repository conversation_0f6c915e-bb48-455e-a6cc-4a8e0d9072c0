import React from 'react';
import { ChatProvider } from '@/contexts/ChatContext';
import { UniversalChat } from '@/components/chat';
import { useAuth } from '@/features/auth/hooks/useAuth';

/**
 * Customer Messages Page
 *
 * Dedicated page for customer messaging functionality using the Universal Chat component.
 */
const CustomerMessages: React.FC = () => {
  const { user } = useAuth();

  return (
    <ChatProvider>
        <div className="h-[calc(100vh-80px)] bg-white rounded-lg shadow-sm border">
          <UniversalChat
            userRole="customer"
            userId={user?.id || 'customer-user'}
            variant="full"
            theme="customer"
            onChatSelect={(chatId) => {
              // console.log('Customer selected chat:', chatId);
            }}
            onMessageSent={(message) => {
              // console.log('Customer sent message:', message);
            }}
            onError={(error) => {
              // console.error('Customer chat error:', error);
            }}
          />
        </div>
    </ChatProvider>
  );
};

export default CustomerMessages;