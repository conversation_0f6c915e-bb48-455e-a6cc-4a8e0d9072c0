/* Fixed Layout Chat Styles */

/* Ensure proper scrolling behavior for fixed chat layout */
.chat-fixed-layout {
  position: fixed;
  inset: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Fixed chat header styling */
.chat-header-fixed {
  position: fixed;
  left: 0;
  right: 0;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  z-index: 40;
  transition: all 0.2s ease-in-out;
}

.dark .chat-header-fixed {
  background: #1f2937;
  border-bottom-color: #374151;
}

/* Fixed chat input styling */
.chat-input-fixed {
  position: fixed;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e5e7eb;
  box-shadow: 0 -1px 3px 0 rgba(0, 0, 0, 0.1);
  z-index: 40;
  transition: all 0.2s ease-in-out;
}

.dark .chat-input-fixed {
  background: #1f2937;
  border-top-color: #374151;
}

/* Scrollable messages area */
.chat-messages-scrollable {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  scroll-behavior: smooth;
  z-index: 10;
}

/* Smooth scrolling for webkit browsers */
.chat-messages-scrollable::-webkit-scrollbar {
  width: 6px;
}

.chat-messages-scrollable::-webkit-scrollbar-track {
  background: transparent;
}

.chat-messages-scrollable::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.chat-messages-scrollable::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

.dark .chat-messages-scrollable::-webkit-scrollbar-thumb {
  background: #4b5563;
}

.dark .chat-messages-scrollable::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Mobile specific adjustments */
@media (max-width: 768px) {
  .chat-header-fixed {
    padding: 0.75rem 1rem;
  }
  
  .chat-input-fixed {
    padding: 0.75rem 1rem;
  }
  
  /* Adjust for mobile keyboards */
  .chat-input-fixed {
    padding-bottom: env(safe-area-inset-bottom, 0.75rem);
  }
}

/* Responsive design for different screen sizes */
@media (min-width: 769px) {
  .chat-header-fixed {
    padding: 1rem 1.5rem;
  }
  
  .chat-input-fixed {
    padding: 1rem 1.5rem;
  }
}

/* Animation for smooth transitions */
.chat-fixed-enter {
  opacity: 0;
  transform: translateY(-10px);
}

.chat-fixed-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
}

.chat-fixed-exit {
  opacity: 1;
  transform: translateY(0);
}

.chat-fixed-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
}

/* Focus states for accessibility */
.chat-header-fixed:focus-within,
.chat-input-fixed:focus-within {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .chat-header-fixed,
  .chat-input-fixed {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .chat-header-fixed,
  .chat-input-fixed,
  .chat-fixed-enter-active,
  .chat-fixed-exit-active {
    transition: none;
  }
  
  .chat-messages-scrollable {
    scroll-behavior: auto;
  }
}

/* Print styles */
@media print {
  .chat-header-fixed,
  .chat-input-fixed {
    position: static;
    box-shadow: none;
  }
  
  .chat-messages-scrollable {
    overflow: visible;
    height: auto;
  }
}
