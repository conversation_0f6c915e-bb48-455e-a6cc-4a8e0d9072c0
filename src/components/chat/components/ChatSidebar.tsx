import React, { useState, useMemo } from 'react';
import { Chat } from '@/types/chat';
import { ChatFeatures, ChatTheme } from '@/components/chat';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Plus,
  Search,
  MessageCircle,
  Users
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { format, isToday, isYesterday } from 'date-fns';

interface ChatSidebarProps {
  chats: Chat[];
  selectedChatId?: string;
  userRole: 'admin' | 'provider' | 'customer';
  features: ChatFeatures;
  theme: ChatTheme;
  isLoading: boolean;
  onChatSelect: (chatId: string) => void;
  onCreateNewChat: () => void;
  isMobile?: boolean;
}

/**
 * Custom comparison function for React.memo
 * Only re-renders when specific props actually change
 */
const arePropsEqual = (prevProps: ChatSidebarProps, nextProps: ChatSidebarProps): boolean => {
  // Compare primitive values
  if (
    prevProps.selectedChatId !== nextProps.selectedChatId ||
    prevProps.isLoading !== nextProps.isLoading ||
    prevProps.userRole !== nextProps.userRole ||
    prevProps.isMobile !== nextProps.isMobile
  ) {
    return false;
  }

  // Compare object references (features and theme)
  if (
    prevProps.features !== nextProps.features ||
    prevProps.theme !== nextProps.theme
  ) {
    return false;
  }

  // Compare chats array
  if (prevProps.chats !== nextProps.chats) {
    // If references are different, check if the content is meaningfully different
    if (prevProps.chats.length !== nextProps.chats.length) {
      return false;
    }

    // Check if any chat has changed by comparing key properties
    for (let i = 0; i < prevProps.chats.length; i++) {
      const prevChat = prevProps.chats[i];
      const nextChat = nextProps.chats[i];

      if (
        prevChat.id !== nextChat.id ||
        prevChat.name !== nextChat.name ||
        prevChat.unread_count !== nextChat.unread_count ||
        prevChat.updated_at !== nextChat.updated_at ||
        prevChat.last_message?.id !== nextChat.last_message?.id ||
        prevChat.last_message?.message !== nextChat.last_message?.message ||
        prevChat.last_message?.created_at !== nextChat.last_message?.created_at
      ) {
        return false;
      }
    }
  }

  // Skip function prop comparisons to avoid unnecessary re-renders
  // Functions like onChatSelect and onCreateNewChat may change on every render
  // but the component behavior remains the same

  return true;
};

/**
 * ChatSidebar Component
 *
 * Displays a list of chats with search functionality and role-based features.
 * Shows chat previews, unread counts, and participant information.
 */
const ChatSidebarComponent: React.FC<ChatSidebarProps> = ({
  chats,
  selectedChatId,
  userRole,
  features,
  theme: _theme,
  isLoading,
  onChatSelect,
  onCreateNewChat,
  isMobile = false,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filter, _setFilter] = useState<'all' | 'unread' | 'groups'>('all');

  // Filter and search chats
  const filteredChats = useMemo(() => {
    let filtered = chats;
    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(chat => {
        // Search in chat name
        if (chat.name?.toLowerCase().includes(query)) return true;
        
        // Search in participant names
        return chat.participants.some(participant => 
          participant.name.toLowerCase().includes(query)
        );
      });
    }
    // Apply type filter
    switch (filter) {
      case 'unread':
        filtered = filtered.filter(chat => (chat.unread_count || 0) > 0);
        break;
      case 'groups':
        filtered = filtered.filter(chat => chat.type === 'group');
        break;
      default:
        break;
    }

    // Sort by last message time
    return filtered.sort((a, b) => {
      const aTime = a.last_message?.created_at || a.updated_at;
      const bTime = b.last_message?.created_at || b.updated_at;
      return new Date(bTime).getTime() - new Date(aTime).getTime();
    });
  }, [chats, searchQuery, filter]);

  // Format last message time
  const formatLastMessageTime = (dateString: string) => {
    const date = new Date(dateString);
    if (isToday(date)) return format(date, 'HH:mm');
    if (isYesterday(date)) return 'Yesterday';
    return format(date, 'MMM d');
  };

  // Get chat display name
  const getChatDisplayName = (chat: Chat) => {
    if (chat.name) return chat.name;
    
    if (chat.type === 'group') {
      return `Group (${chat.participants.length})`;
    }
    
    // For direct chats, show the other participant's name
    const otherParticipant = chat.participants.find(p => p.role?.name !== userRole);
    return otherParticipant?.name || 'Unknown User';
  };

  // Get chat avatar
  const getChatAvatar = (chat: Chat) => {
    if (chat.type === 'group') {
      // For groups, show first participant's avatar or group icon
      return chat.participants[0]?.avatar;
    }
    
    // For direct chats, show the other participant's avatar
    const otherParticipant = chat.participants.find(p => p.role?.name !== userRole);
    return otherParticipant?.avatar;
  };

  // Get chat avatar fallback
  const getChatAvatarFallback = (chat: Chat) => {
    if (chat.type === 'group') {
      return <Users className="h-4 w-4" />;
    }
    
    const otherParticipant = chat.participants.find(p => p.role?.name !== userRole);
    return otherParticipant?.name.charAt(0).toUpperCase() || '?';
  };

  // Memoized individual chat item component to prevent unnecessary re-renders
  const ChatItem = React.memo(({
    chat,
    isSelected,
    userRole,
    features,
    onChatSelect
  }: {
    chat: Chat;
    isSelected: boolean;
    userRole: 'admin' | 'provider' | 'customer';
    features: ChatFeatures;
    onChatSelect: (chatId: string) => void;
  }) => {
    const hasUnread = (chat.unread_count || 0) > 0;
    const lastMessageTime = chat.last_message?.created_at || chat.updated_at;

    return (
      <div
        key={chat.id}
        className={cn(
          'flex items-center space-x-3 p-3 cursor-pointer hover:bg-[var(--chat-surface)] transition-colors border-l-2',
          isSelected 
            ? 'bg-[var(--chat-primary)]/10 border-l-[var(--chat-primary)]' 
            : 'border-l-transparent'
        )}
        onClick={(e) => {
          e.preventDefault();
          onChatSelect(chat.id);
        }}
      >
        {/* Avatar */}
        <div className="relative flex-shrink-0">
          <Avatar className="w-10 h-10">
            <AvatarImage src={getChatAvatar(chat)} />
            <AvatarFallback className="text-sm">
              {getChatAvatarFallback(chat)}
            </AvatarFallback>
          </Avatar>
          
          {/* Online indicator for direct chats */}
          {chat.type === 'direct' && features.canSeeOnlineStatus && (
            (() => {
              const otherParticipant = chat.participants.find(p => p.role.name !== userRole);
              return otherParticipant?.is_online && (
                <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white" />
              );
            })()
          )}
        </div>

        {/* Chat info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <h4 className={cn(
              'text-sm font-medium truncate',
              hasUnread ? 'text-[var(--chat-text)]' : 'text-[var(--chat-text-secondary)]'
            )}>
              {getChatDisplayName(chat)}
            </h4>
            
            <div className="flex items-center space-x-1 flex-shrink-0">
              {/* Unread count */}
              {hasUnread && (
                <Badge 
                  variant="default" 
                  className="h-5 min-w-[20px] text-xs bg-[var(--chat-primary)] text-white"
                >
                  {chat.unread_count}
                </Badge>
              )}
              
              {/* Last message time */}
              <span className="text-xs text-[var(--chat-text-secondary)]">
                {formatLastMessageTime(lastMessageTime)}
              </span>
            </div>
          </div>
          
          {/* Last message preview */}
          <div className="flex items-center space-x-1">
            {chat.last_message && (
              <>
                {/* Message sender (for groups) */}
                {chat.type === 'group' && (
                  <span className="text-xs text-[var(--chat-text-secondary)] flex-shrink-0">
                    {chat.last_message.user.name}:
                  </span>
                )}
                
                {/* Message content */}
                <p className={cn(
                  'text-xs truncate',
                  hasUnread ? 'text-[var(--chat-text)]' : 'text-[var(--chat-text-secondary)]'
                )}>
                  {chat.last_message.message}
                </p>
              </>
            )}
            
            {!chat.last_message && (
              <p className="text-xs text-[var(--chat-text-secondary)] italic">
                No messages yet
              </p>
            )}
          </div>
        </div>
      </div>
    );
  });

  // Add displayName for ChatItem
  ChatItem.displayName = 'ChatItem';

  // Render individual chat item
  const renderChatItem = (chat: Chat) => (
    <ChatItem
      key={chat.id}
      chat={chat}
      isSelected={chat.id === selectedChatId}
      userRole={userRole}
      features={features}
      onChatSelect={onChatSelect}
    />
  );

  // Render loading state
  const renderLoadingState = () => (
    <div className="space-y-3 p-3">
      {[...Array(5)].map((_, i) => (
        <div key={i} className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-[var(--chat-border)] rounded-full animate-pulse" />
          <div className="flex-1 space-y-2">
            <div className="w-3/4 h-4 bg-[var(--chat-border)] rounded animate-pulse" />
            <div className="w-1/2 h-3 bg-[var(--chat-border)] rounded animate-pulse" />
          </div>
        </div>
      ))}
    </div>
  );

  // Render empty state
  const renderEmptyState = () => (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      <MessageCircle className="h-12 w-12 text-[var(--chat-text-secondary)] mb-4" />
      <h3 className="text-lg font-medium text-[var(--chat-text)] mb-2">
        No conversations yet
      </h3>
      <p className="text-sm text-[var(--chat-text-secondary)] mb-4">
        Start a new conversation to begin messaging
      </p>
    </div>
  );

  return (
    <div className={cn(
      'chat-sidebar flex flex-col h-full bg-[var(--chat-background)] border-r border-[var(--chat-border)]',
      isMobile ? 'w-full' : 'w-80'
    )}>
      {/* Header */}
      <div className="p-4 border-b border-[var(--chat-border)]">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-[var(--chat-text)]">
            Messages
          </h2>
        </div>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--chat-text-secondary)]" />
          <Input
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-[var(--chat-surface)] border-[var(--chat-border)]"
          />
        </div>
      </div>

      {/* Chat list */}
      <ScrollArea className="flex-1">
        {isLoading ? (
          renderLoadingState()
        ) : filteredChats.length === 0 ? (
          searchQuery ? (
            <div className="p-8 text-center">
              <p className="text-sm text-[var(--chat-text-secondary)]">
                No conversations found for "{searchQuery}"
              </p>
            </div>
          ) : (
            renderEmptyState()
          )
        ) : (
          <div className="divide-y divide-[var(--chat-border)]">
            {filteredChats.map(renderChatItem)}
          </div>
        )}
      </ScrollArea>
    </div>
  );
};

// Add displayName for better debugging
ChatSidebarComponent.displayName = 'ChatSidebar';

// Wrap component with React.memo and custom comparison function
export const ChatSidebar = React.memo(ChatSidebarComponent, arePropsEqual);

export default ChatSidebar;
